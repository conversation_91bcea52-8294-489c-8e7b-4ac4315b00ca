# backend/tests/test_api/test_electrical_routes.py
"""
Test suite for electrical API routes.

This module tests all electrical-related API endpoints including:
- Electrical node CRUD operations
- Cable route management
- Load calculations
- Electrical calculations (cable sizing, voltage drop)
- Business logic endpoints (optimization, design workflow)
"""

import uuid
import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session

from core.models.project import Project
from core.models.users import User

pytestmark = [
    pytest.mark.unit,
    pytest.mark.api,
    pytest.mark.integration,
    pytest.mark.electrical,
]


class TestElectricalNodeRoutes:
    """Test electrical node API endpoints."""

    def test_create_electrical_node_success(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test creating an electrical node successfully."""
        node_data = {
            "project_id": sample_project.id,
            "name": "Main Distribution Panel",
            "node_type": "SWITCHBOARD_INCOMING",
            "voltage_v": 480.0,
            "location_description": "Electrical Room A",
            "power_capacity_kva": 1000.0,
        }

        with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo:
            mock_instance = MagicMock()
            mock_repo.return_value = mock_instance

            # Mock the created node
            mock_node = MagicMock()
            mock_node.id = 1
            mock_node.name = node_data["name"]
            mock_node.project_id = sample_project.id
            mock_instance.create.return_value = mock_node

            response = client.post("/api/v1/electrical/nodes", json=node_data)

            assert response.status_code == 201
            data = response.json()
            assert data["name"] == node_data["name"]
            assert data["project_id"] == sample_project.id
            mock_instance.create.assert_called_once()

    def test_create_electrical_node_validation_error(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test creating electrical node with invalid data."""
        invalid_data = {
            "name": "",  # Empty name should fail validation
            "node_type": "invalid_type",
            "voltage_level": -100.0,  # Negative voltage should fail
        }

        response = client.post("/api/v1/electrical/nodes", json=invalid_data)
        assert response.status_code == 422

    def test_get_electrical_node_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test retrieving an electrical node by ID."""
        node_id = 1

        with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo:
            mock_instance = MagicMock()
            mock_repo.return_value = mock_instance

            # Mock the retrieved node
            mock_node = MagicMock()
            mock_node.id = node_id
            mock_node.name = "Test Node"
            mock_instance.get_by_id.return_value = mock_node

            response = client.get(f"/api/v1/electrical/nodes/{node_id}")

            assert response.status_code == 200
            data = response.json()
            assert data["id"] == node_id
            mock_instance.get_by_id.assert_called_once_with(node_id)

    def test_get_electrical_node_not_found(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test retrieving non-existent electrical node."""
        node_id = 999

        with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo:
            mock_instance = MagicMock()
            mock_repo.return_value = mock_instance
            mock_instance.get_by_id.return_value = None

            response = client.get(f"/api/v1/electrical/nodes/{node_id}")

            assert response.status_code == 404
            assert "not found" in response.json()["detail"].lower()

    def test_list_electrical_nodes_success(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test listing electrical nodes for a project."""
        with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo:
            mock_instance = MagicMock()
            mock_repo.return_value = mock_instance

            # Mock the node list
            mock_nodes = [
                MagicMock(id=1, name="Node 1"),
                MagicMock(id=2, name="Node 2"),
            ]
            mock_instance.get_by_project_id.return_value = mock_nodes
            mock_instance.count_by_project.return_value = 2

            response = client.get(
                f"/api/v1/electrical/nodes?project_id={sample_project.id}&skip=0&limit=10"
            )

            assert response.status_code == 200
            data = response.json()
            assert "electrical_nodes" in data
            assert data["total"] == 2
            assert len(data["electrical_nodes"]) == 2

    def test_update_electrical_node_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test updating an electrical node."""
        node_id = 1
        update_data = {
            "name": "Updated Node Name",
            "voltage_level": 600.0,
        }

        with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo:
            mock_instance = MagicMock()
            mock_repo.return_value = mock_instance

            # Mock existing node
            mock_existing = MagicMock()
            mock_existing.id = node_id
            mock_instance.get_by_id.return_value = mock_existing

            # Mock updated node
            mock_updated = MagicMock()
            mock_updated.id = node_id
            mock_updated.name = update_data["name"]
            mock_instance.update.return_value = mock_updated

            response = client.put(
                f"/api/v1/electrical/nodes/{node_id}", json=update_data
            )

            assert response.status_code == 200
            data = response.json()
            assert data["name"] == update_data["name"]
            mock_instance.update.assert_called_once()

    def test_delete_electrical_node_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test soft deleting an electrical node."""
        node_id = 1

        with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo:
            mock_instance = MagicMock()
            mock_repo.return_value = mock_instance

            # Mock existing node
            mock_existing = MagicMock()
            mock_existing.id = node_id
            mock_instance.get_by_id.return_value = mock_existing

            response = client.delete(f"/api/v1/electrical/nodes/{node_id}")

            assert response.status_code == 204
            mock_instance.soft_delete.assert_called_once_with(node_id)


class TestCableRouteRoutes:
    """Test cable route API endpoints."""

    def test_create_cable_route_success(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test creating a cable route successfully."""
        route_data = {
            "project_id": sample_project.id,
            "name": "Main Feeder Route",
            "source_node_id": 1,
            "destination_node_id": 2,
            "cable_type": "THWN",
            "conductor_size_awg": "4/0",
            "conductor_count": 3,
            "length_meters": 150.0,
            "installation_method": "conduit",
            "conduit_type": "EMT",
            "ambient_temperature_c": 30.0,
        }

        with patch("api.v1.electrical_routes.CableRouteRepository") as mock_repo:
            mock_instance = MagicMock()
            mock_repo.return_value = mock_instance

            # Mock the created route
            mock_route = MagicMock()
            mock_route.id = 1
            mock_route.name = route_data["name"]
            mock_route.project_id = sample_project.id
            mock_instance.create.return_value = mock_route

            response = client.post("/api/v1/electrical/cable-routes", json=route_data)

            assert response.status_code == 201
            data = response.json()
            assert data["name"] == route_data["name"]
            assert data["project_id"] == sample_project.id
            mock_instance.create.assert_called_once()

    def test_get_cable_route_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test retrieving a cable route by ID."""
        route_id = 1

        with patch("api.v1.electrical_routes.CableRouteRepository") as mock_repo:
            mock_instance = MagicMock()
            mock_repo.return_value = mock_instance

            # Mock the retrieved route
            mock_route = MagicMock()
            mock_route.id = route_id
            mock_route.name = "Test Route"
            mock_instance.get_by_id.return_value = mock_route

            response = client.get(f"/api/v1/electrical/cable-routes/{route_id}")

            assert response.status_code == 200
            data = response.json()
            assert data["id"] == route_id
            mock_instance.get_by_id.assert_called_once_with(route_id)

    def test_list_cable_routes_success(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test listing cable routes for a project."""
        with patch("api.v1.electrical_routes.CableRouteRepository") as mock_repo:
            mock_instance = MagicMock()
            mock_repo.return_value = mock_instance

            # Mock the route list
            mock_routes = [
                MagicMock(id=1, name="Route 1"),
                MagicMock(id=2, name="Route 2"),
            ]
            mock_instance.get_by_project_id.return_value = mock_routes
            mock_instance.count_by_project.return_value = 2

            response = client.get(
                f"/api/v1/electrical/cable-routes?project_id={sample_project.id}&skip=0&limit=10"
            )

            assert response.status_code == 200
            data = response.json()
            assert "cable_routes" in data
            assert data["total"] == 2
            assert len(data["cable_routes"]) == 2


class TestLoadCalculationRoutes:
    """Test load calculation API endpoints."""

    def test_create_load_calculation_success(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test creating a load calculation successfully."""
        calc_data = {
            "project_id": sample_project.id,
            "electrical_node_id": 1,
            "calculation_name": "Motor Load Calculation",
            "load_type": "motor",
            "connected_load_kw": 50.0,
            "demand_factor": 0.8,
            "power_factor": 0.85,
            "efficiency": 0.92,
            "voltage_level": 480.0,
            "phase_count": 3,
        }

        with patch("api.v1.electrical_routes.LoadCalculationRepository") as mock_repo:
            mock_instance = MagicMock()
            mock_repo.return_value = mock_instance

            # Mock the created calculation
            mock_calc = MagicMock()
            mock_calc.id = 1
            mock_calc.calculation_name = calc_data["calculation_name"]
            mock_calc.project_id = sample_project.id
            mock_instance.create.return_value = mock_calc

            response = client.post(
                "/api/v1/electrical/load-calculations", json=calc_data
            )

            assert response.status_code == 201
            data = response.json()
            assert data["calculation_name"] == calc_data["calculation_name"]
            assert data["project_id"] == sample_project.id
            mock_instance.create.assert_called_once()

    def test_get_load_calculation_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test retrieving a load calculation by ID."""
        calc_id = 1

        with patch("api.v1.electrical_routes.LoadCalculationRepository") as mock_repo:
            mock_instance = MagicMock()
            mock_repo.return_value = mock_instance

            # Mock the retrieved calculation
            mock_calc = MagicMock()
            mock_calc.id = calc_id
            mock_calc.calculation_name = "Test Calculation"
            mock_instance.get_by_id.return_value = mock_calc

            response = client.get(f"/api/v1/electrical/load-calculations/{calc_id}")

            assert response.status_code == 200
            data = response.json()
            assert data["id"] == calc_id
            mock_instance.get_by_id.assert_called_once_with(calc_id)

    def test_list_load_calculations_success(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test listing load calculations for a project."""
        with patch("api.v1.electrical_routes.LoadCalculationRepository") as mock_repo:
            mock_instance = MagicMock()
            mock_repo.return_value = mock_instance

            # Mock the calculation list
            mock_calcs = [
                MagicMock(id=1, calculation_name="Calc 1"),
                MagicMock(id=2, calculation_name="Calc 2"),
            ]
            mock_instance.get_by_project_id.return_value = mock_calcs
            mock_instance.count_by_project.return_value = 2

            response = client.get(
                f"/api/v1/electrical/load-calculations?project_id={sample_project.id}&skip=0&limit=10"
            )

            assert response.status_code == 200
            data = response.json()
            assert "load_calculations" in data
            assert data["total"] == 2
            assert len(data["load_calculations"]) == 2


class TestElectricalCalculationRoutes:
    """Test electrical calculation API endpoints."""

    def test_cable_sizing_calculation_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test cable sizing calculation."""
        calc_data = {
            "load_current_amps": 100.0,
            "cable_length_meters": 150.0,
            "voltage_level": 480.0,
            "installation_method": "conduit",
            "ambient_temperature_c": 30.0,
            "conductor_material": "copper",
            "insulation_type": "THWN",
            "voltage_drop_limit_percent": 3.0,
            "safety_factor": 1.25,
        }

        with patch("api.v1.electrical_routes.ElectricalService") as mock_service:
            mock_instance = MagicMock()
            mock_service.return_value = mock_instance

            # Mock calculation result
            mock_result = {
                "recommended_conductor_size": "4/0 AWG",
                "calculated_voltage_drop_percent": 2.1,
                "ampacity_rating": 125.0,
                "meets_requirements": True,
                "calculation_details": {
                    "resistance_per_meter": 0.000164,
                    "total_resistance": 0.0246,
                    "voltage_drop_volts": 10.08,
                },
            }
            mock_instance.calculate_cable_sizing.return_value = mock_result

            response = client.post(
                "/api/v1/electrical/calculations/cable-sizing", json=calc_data
            )

            assert response.status_code == 200
            data = response.json()
            assert data["recommended_conductor_size"] == "4/0 AWG"
            assert data["meets_requirements"] is True
            mock_instance.calculate_cable_sizing.assert_called_once()

    def test_voltage_drop_calculation_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test voltage drop calculation."""
        calc_data = {
            "load_current_amps": 80.0,
            "cable_length_meters": 100.0,
            "conductor_size_awg": "2/0",
            "voltage_level": 480.0,
            "conductor_material": "copper",
            "power_factor": 0.85,
            "phase_count": 3,
        }

        with patch("api.v1.electrical_routes.ElectricalService") as mock_service:
            mock_instance = MagicMock()
            mock_service.return_value = mock_instance

            # Mock calculation result
            mock_result = {
                "voltage_drop_volts": 8.5,
                "voltage_drop_percent": 1.77,
                "voltage_at_load": 471.5,
                "meets_standards": True,
                "calculation_details": {
                    "resistance_per_meter": 0.000328,
                    "reactance_per_meter": 0.000164,
                    "impedance_per_meter": 0.000367,
                },
            }
            mock_instance.calculate_voltage_drop.return_value = mock_result

            response = client.post(
                "/api/v1/electrical/calculations/voltage-drop", json=calc_data
            )

            assert response.status_code == 200
            data = response.json()
            assert data["voltage_drop_percent"] == 1.77
            assert data["meets_standards"] is True
            mock_instance.calculate_voltage_drop.assert_called_once()

    def test_standards_validation_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test electrical standards validation."""
        validation_data = {
            "project_id": 1,
            "electrical_standard": "NEC_2020",
            "validation_scope": "complete_project",
            "include_warnings": True,
        }

        with patch("api.v1.electrical_routes.ElectricalService") as mock_service:
            mock_instance = MagicMock()
            mock_service.return_value = mock_instance

            # Mock validation result
            mock_result = {
                "overall_compliance": True,
                "validation_summary": {
                    "total_checks": 25,
                    "passed_checks": 23,
                    "failed_checks": 0,
                    "warnings": 2,
                },
                "detailed_results": [
                    {
                        "check_name": "Voltage Drop Compliance",
                        "status": "PASS",
                        "details": "All circuits meet voltage drop requirements",
                    },
                    {
                        "check_name": "Conductor Sizing",
                        "status": "WARNING",
                        "details": "Some conductors are oversized",
                    },
                ],
            }
            mock_instance.validate_electrical_standards.return_value = mock_result

            response = client.post(
                "/api/v1/electrical/calculations/standards-validation",
                json=validation_data,
            )

            assert response.status_code == 200
            data = response.json()
            assert data["overall_compliance"] is True
            assert data["validation_summary"]["total_checks"] == 25
            mock_instance.validate_electrical_standards.assert_called_once()


class TestElectricalBusinessLogicRoutes:
    """Test electrical business logic API endpoints."""

    def test_node_load_summary_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test getting electrical node load summary."""
        node_id = 1

        with patch("api.v1.electrical_routes.ElectricalService") as mock_service:
            mock_instance = MagicMock()
            mock_service.return_value = mock_instance

            # Mock load summary
            mock_summary = {
                "node_id": node_id,
                "total_connected_load_kw": 250.0,
                "total_demand_load_kw": 200.0,
                "load_factor": 0.8,
                "diversity_factor": 0.85,
                "load_breakdown": {
                    "motor_loads": 150.0,
                    "lighting_loads": 50.0,
                    "receptacle_loads": 50.0,
                },
                "peak_demand_time": "14:30",
                "load_profile_24h": [
                    100,
                    95,
                    90,
                    85,
                    80,
                    85,
                    90,
                    95,
                    100,
                    105,
                    110,
                    115,
                    120,
                    125,
                    130,
                    125,
                    120,
                    115,
                    110,
                    105,
                    100,
                    95,
                    90,
                    85,
                ],
            }
            mock_instance.get_node_load_summary.return_value = mock_summary

            response = client.get(f"/api/v1/electrical/nodes/{node_id}/load-summary")

            assert response.status_code == 200
            data = response.json()
            assert data["node_id"] == node_id
            assert data["total_connected_load_kw"] == 250.0
            mock_instance.get_node_load_summary.assert_called_once_with(node_id)

    def test_optimize_cable_route_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test cable route optimization."""
        route_id = 1
        optimization_data = {
            "optimization_criteria": "cost",
            "constraints": {
                "max_voltage_drop_percent": 3.0,
                "min_safety_factor": 1.25,
                "preferred_conductor_material": "copper",
            },
            "consider_alternatives": True,
        }

        with patch("api.v1.electrical_routes.ElectricalService") as mock_service:
            mock_instance = MagicMock()
            mock_service.return_value = mock_instance

            # Mock optimization result
            mock_result = {
                "original_configuration": {
                    "conductor_size": "4/0 AWG",
                    "estimated_cost": 2500.0,
                    "voltage_drop_percent": 2.1,
                },
                "optimized_configuration": {
                    "conductor_size": "3/0 AWG",
                    "estimated_cost": 2100.0,
                    "voltage_drop_percent": 2.8,
                },
                "cost_savings": 400.0,
                "optimization_summary": "Reduced conductor size while maintaining compliance",
            }
            mock_instance.optimize_cable_route.return_value = mock_result

            response = client.post(
                f"/api/v1/electrical/cable-routes/{route_id}/optimize",
                json=optimization_data,
            )

            assert response.status_code == 200
            data = response.json()
            assert data["cost_savings"] == 400.0
            assert "optimized_configuration" in data
            mock_instance.optimize_cable_route.assert_called_once()

    def test_design_workflow_success(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test complete electrical design workflow."""
        workflow_data = {
            "project_id": sample_project.id,
            "design_criteria": {
                "electrical_standard": "NEC_2020",
                "voltage_levels": [480, 208, 120],
                "load_growth_factor": 1.25,
                "diversity_factors": {
                    "motor_loads": 0.8,
                    "lighting_loads": 1.0,
                    "receptacle_loads": 0.5,
                },
            },
            "optimization_preferences": {
                "primary_objective": "cost",
                "secondary_objective": "efficiency",
                "consider_future_expansion": True,
            },
        }

        with patch("api.v1.electrical_routes.ElectricalService") as mock_service:
            mock_instance = MagicMock()
            mock_service.return_value = mock_instance

            # Mock workflow result
            mock_result = {
                "design_summary": {
                    "total_nodes": 15,
                    "total_cable_routes": 12,
                    "total_load_calculations": 8,
                    "estimated_total_cost": 45000.0,
                },
                "compliance_status": {
                    "overall_compliance": True,
                    "standards_met": ["NEC_2020", "IEEE_141"],
                    "warnings": [],
                },
                "optimization_results": {
                    "cost_optimized": True,
                    "efficiency_rating": "A",
                    "future_expansion_ready": True,
                },
                "next_steps": [
                    "Review and approve design",
                    "Generate construction documents",
                    "Submit for permit approval",
                ],
            }
            mock_instance.execute_design_workflow.return_value = mock_result

            response = client.post(
                "/api/v1/electrical/design-workflow", json=workflow_data
            )

            assert response.status_code == 200
            data = response.json()
            assert data["design_summary"]["total_nodes"] == 15
            assert data["compliance_status"]["overall_compliance"] is True
            mock_instance.execute_design_workflow.assert_called_once()
