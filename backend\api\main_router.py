# src/api/main_router.py
from fastapi import APIRouter

# Import versioned routers
from .v1.project_routes import router as v1_project_router
from .v1.component_routes import router as v1_component_router
from .v1.heat_tracing_routes import router as v1_heat_tracing_router
from .v1.electrical_routes import router as v1_electrical_router
from .v1.switchboard_routes import router as v1_switchboard_router
from .v1.user_routes import router as v1_user_router
from .v1.document_routes import router as v1_document_router
from .v1.activity_log_routes import router as v1_activity_log_router
from .v1.import_export_routes import router as v1_import_export_router

api_router = APIRouter()

# Include versioned routers, typically with a version prefix
api_router.include_router(v1_project_router, prefix="/v1/projects", tags=["Projects"])
api_router.include_router(
    v1_component_router, prefix="/v1/components", tags=["Components"]
)
api_router.include_router(
    v1_heat_tracing_router, prefix="/v1/heat-tracing", tags=["Heat Tracing"]
)
api_router.include_router(
    v1_electrical_router, prefix="/v1/electrical", tags=["Electrical"]
)
api_router.include_router(
    v1_switchboard_router, prefix="/v1/switchboards", tags=["Switchboards"]
)
api_router.include_router(v1_user_router, prefix="/v1/users", tags=["Users"])
api_router.include_router(
    v1_document_router, prefix="/v1/documents", tags=["Documents"]
)
api_router.include_router(v1_activity_log_router, prefix="/v1", tags=["Activity Logs"])
api_router.include_router(v1_import_export_router, prefix="/v1", tags=["Import/Export"])
