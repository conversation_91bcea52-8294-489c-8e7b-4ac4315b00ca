#!/usr/bin/env python3
# backend/run_tests.py
"""
Test Runner for Ultimate Electrical Designer Backend.

This script provides a comprehensive test runner with various options
for running different types of tests and generating reports.
"""

import os
import sys
import argparse
import subprocess
import time
from pathlib import Path
from typing import List, Optional


def run_command(command: List[str], cwd: Optional[str] = None) -> int:
    """Run a command and return the exit code."""
    print(f"Running: {' '.join(command)}")
    if cwd:
        print(f"Working directory: {cwd}")
    
    try:
        result = subprocess.run(command, cwd=cwd, check=False)
        return result.returncode
    except Exception as e:
        print(f"Error running command: {e}")
        return 1


def run_unit_tests(verbose: bool = False, coverage: bool = False) -> int:
    """Run unit tests."""
    print("\n" + "="*60)
    print("RUNNING UNIT TESTS")
    print("="*60)
    
    command = ["python", "-m", "pytest", "tests/test_calculations/", "tests/test_import_export/", 
               "tests/test_reports/", "tests/test_standards/", "-m", "unit"]
    
    if verbose:
        command.append("-v")
    
    if coverage:
        command.extend(["--cov=core", "--cov-report=html", "--cov-report=term"])
    
    return run_command(command)


def run_integration_tests(verbose: bool = False) -> int:
    """Run integration tests."""
    print("\n" + "="*60)
    print("RUNNING INTEGRATION TESTS")
    print("="*60)
    
    command = ["python", "-m", "pytest", "tests/test_integration/", "-m", "integration"]
    
    if verbose:
        command.append("-v")
    
    return run_command(command)


def run_api_tests(verbose: bool = False) -> int:
    """Run API tests."""
    print("\n" + "="*60)
    print("RUNNING API TESTS")
    print("="*60)
    
    command = ["python", "-m", "pytest", "tests/test_api/", "-m", "api"]
    
    if verbose:
        command.append("-v")
    
    return run_command(command)


def run_performance_tests(verbose: bool = False) -> int:
    """Run performance tests."""
    print("\n" + "="*60)
    print("RUNNING PERFORMANCE TESTS")
    print("="*60)
    
    command = ["python", "-m", "pytest", "tests/", "-m", "performance"]
    
    if verbose:
        command.append("-v")
    
    return run_command(command)


def run_all_tests(verbose: bool = False, coverage: bool = False) -> int:
    """Run all tests."""
    print("\n" + "="*60)
    print("RUNNING ALL TESTS")
    print("="*60)
    
    command = ["python", "-m", "pytest", "tests/"]
    
    if verbose:
        command.append("-v")
    
    if coverage:
        command.extend(["--cov=core", "--cov-report=html", "--cov-report=term"])
    
    return run_command(command)


def run_specific_test(test_path: str, verbose: bool = False) -> int:
    """Run a specific test file or test function."""
    print(f"\n" + "="*60)
    print(f"RUNNING SPECIFIC TEST: {test_path}")
    print("="*60)
    
    command = ["python", "-m", "pytest", test_path]
    
    if verbose:
        command.append("-v")
    
    return run_command(command)


def run_linting() -> int:
    """Run code linting."""
    print("\n" + "="*60)
    print("RUNNING CODE LINTING")
    print("="*60)
    
    # Run flake8
    print("\nRunning flake8...")
    flake8_result = run_command(["python", "-m", "flake8", "core/", "api/", "tests/"])
    
    # Run black check
    print("\nRunning black check...")
    black_result = run_command(["python", "-m", "black", "--check", "core/", "api/", "tests/"])
    
    # Run isort check
    print("\nRunning isort check...")
    isort_result = run_command(["python", "-m", "isort", "--check-only", "core/", "api/", "tests/"])
    
    return max(flake8_result, black_result, isort_result)


def run_type_checking() -> int:
    """Run type checking with mypy."""
    print("\n" + "="*60)
    print("RUNNING TYPE CHECKING")
    print("="*60)
    
    return run_command(["python", "-m", "mypy", "core/", "api/"])


def generate_test_report() -> int:
    """Generate comprehensive test report."""
    print("\n" + "="*60)
    print("GENERATING TEST REPORT")
    print("="*60)
    
    # Run tests with coverage and generate reports
    command = [
        "python", "-m", "pytest", "tests/",
        "--cov=core",
        "--cov=api", 
        "--cov-report=html:htmlcov",
        "--cov-report=xml:coverage.xml",
        "--cov-report=term",
        "--junit-xml=test-results.xml",
        "--html=test-report.html",
        "--self-contained-html"
    ]
    
    return run_command(command)


def install_test_dependencies() -> int:
    """Install test dependencies."""
    print("\n" + "="*60)
    print("INSTALLING TEST DEPENDENCIES")
    print("="*60)
    
    dependencies = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.0.0",
        "pytest-html>=3.1.0",
        "pytest-mock>=3.10.0",
        "coverage>=7.0.0",
        "flake8>=6.0.0",
        "black>=23.0.0",
        "isort>=5.12.0",
        "mypy>=1.0.0",
        "httpx>=0.24.0",  # For FastAPI testing
    ]
    
    for dep in dependencies:
        result = run_command(["pip", "install", dep])
        if result != 0:
            print(f"Failed to install {dep}")
            return result
    
    return 0


def clean_test_artifacts():
    """Clean test artifacts and cache files."""
    print("\n" + "="*60)
    print("CLEANING TEST ARTIFACTS")
    print("="*60)
    
    artifacts = [
        ".pytest_cache",
        "__pycache__",
        "htmlcov",
        "coverage.xml",
        "test-results.xml",
        "test-report.html",
        ".coverage",
        ".mypy_cache",
    ]
    
    for artifact in artifacts:
        if os.path.exists(artifact):
            if os.path.isdir(artifact):
                import shutil
                shutil.rmtree(artifact)
                print(f"Removed directory: {artifact}")
            else:
                os.remove(artifact)
                print(f"Removed file: {artifact}")
    
    # Clean __pycache__ directories recursively
    for root, dirs, files in os.walk("."):
        for dir_name in dirs:
            if dir_name == "__pycache__":
                dir_path = os.path.join(root, dir_name)
                import shutil
                shutil.rmtree(dir_path)
                print(f"Removed cache directory: {dir_path}")


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(
        description="Test runner for Ultimate Electrical Designer Backend",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_tests.py --all                    # Run all tests
  python run_tests.py --unit --coverage        # Run unit tests with coverage
  python run_tests.py --integration            # Run integration tests
  python run_tests.py --api                    # Run API tests
  python run_tests.py --performance            # Run performance tests
  python run_tests.py --specific tests/test_calculations/test_heat_loss_calculator.py
  python run_tests.py --lint                   # Run linting
  python run_tests.py --type-check             # Run type checking
  python run_tests.py --report                 # Generate comprehensive report
  python run_tests.py --install-deps           # Install test dependencies
  python run_tests.py --clean                  # Clean test artifacts
        """
    )
    
    # Test type options
    parser.add_argument("--all", action="store_true", help="Run all tests")
    parser.add_argument("--unit", action="store_true", help="Run unit tests")
    parser.add_argument("--integration", action="store_true", help="Run integration tests")
    parser.add_argument("--api", action="store_true", help="Run API tests")
    parser.add_argument("--performance", action="store_true", help="Run performance tests")
    parser.add_argument("--specific", type=str, help="Run specific test file or function")
    
    # Quality checks
    parser.add_argument("--lint", action="store_true", help="Run code linting")
    parser.add_argument("--type-check", action="store_true", help="Run type checking")
    
    # Reporting
    parser.add_argument("--report", action="store_true", help="Generate comprehensive test report")
    parser.add_argument("--coverage", action="store_true", help="Include coverage analysis")
    
    # Utilities
    parser.add_argument("--install-deps", action="store_true", help="Install test dependencies")
    parser.add_argument("--clean", action="store_true", help="Clean test artifacts")
    
    # Options
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    # Change to backend directory
    backend_dir = Path(__file__).parent
    os.chdir(backend_dir)
    
    # Add backend to Python path
    sys.path.insert(0, str(backend_dir))
    
    start_time = time.time()
    exit_code = 0
    
    try:
        if args.install_deps:
            exit_code = install_test_dependencies()
        elif args.clean:
            clean_test_artifacts()
        elif args.lint:
            exit_code = run_linting()
        elif args.type_check:
            exit_code = run_type_checking()
        elif args.report:
            exit_code = generate_test_report()
        elif args.unit:
            exit_code = run_unit_tests(args.verbose, args.coverage)
        elif args.integration:
            exit_code = run_integration_tests(args.verbose)
        elif args.api:
            exit_code = run_api_tests(args.verbose)
        elif args.performance:
            exit_code = run_performance_tests(args.verbose)
        elif args.specific:
            exit_code = run_specific_test(args.specific, args.verbose)
        elif args.all:
            exit_code = run_all_tests(args.verbose, args.coverage)
        else:
            # Default: run all tests
            exit_code = run_all_tests(args.verbose, args.coverage)
    
    except KeyboardInterrupt:
        print("\nTest execution interrupted by user")
        exit_code = 130
    except Exception as e:
        print(f"\nError during test execution: {e}")
        exit_code = 1
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n" + "="*60)
    print(f"TEST EXECUTION COMPLETED")
    print(f"Duration: {duration:.2f} seconds")
    print(f"Exit code: {exit_code}")
    print("="*60)
    
    if exit_code == 0:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed or errors occurred")
    
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
