[tool:pytest]
# Pytest configuration for Ultimate Electrical Designer Backend

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts =
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=backend/core
    --cov=backend/api
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=85

# Markers
markers =
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    slow: Slow running tests
    api: API endpoint tests
    repository: Repository layer tests
    service: Service layer tests
    schema: Schema validation tests
    performance: Performance and load tests
    smoke: Quick smoke tests for basic functionality
    regression: Regression tests for bug fixes
    security: Security-related tests
    database: Tests that require database access
    external: Tests that require external services
    calculations: Tests for calculation engines
    import_export: Tests for import/export functionality
    reports: Tests for report generation
    standards: Tests for standards validation

# Minimum version
minversion = 6.0

# Test session configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:sqlalchemy.*
    ignore::UserWarning:pydantic.*

# Test timeout (in seconds)
timeout = 300

# Asyncio configuration
asyncio_mode = auto

# Test collection
collect_ignore = [
    "setup.py",
    "migrations/",
    "alembic/",
    "venv/",
    ".venv/",
    "node_modules/",
]

# Additional options
addopts_extra =
    --durations=10
    --color=yes
    --strict-config
